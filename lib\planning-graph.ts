// import { StateGraph, END } from "@langchain/langgraph"
import { ChatOpenAI } from "@langchain/openai"
import { HumanMessage, SystemMessage } from "@langchain/core/messages"

interface PlanningState {
  prompt: string
  isInteractive: boolean
  userAnswers: Record<string, string>
  currentStep: string
  results: Record<string, any>
  needsInput?: boolean
  question?: {
    id: string
    question: string
    type: string
    placeholder?: string
    optional?: boolean
  }
  completed: boolean
}

export class PlanningGraph {
  private model: ChatOpenAI

  constructor() {
    // Initialize OpenRouter Claude 3.5
    this.model = new ChatOpenAI({
      modelName: "anthropic/claude-3.5-sonnet",
      openAIApiKey: process.env.OPENROUTER_API_KEY,
      configuration: {
        baseURL: "https://openrouter.ai/api/v1",
        defaultHeaders: {
          "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
          "X-Title": "AG3NT.png",
        },
      },
      temperature: 0.7,
    })
  }



  async execute(initialState: Partial<PlanningState>): Promise<any> {
    const state: PlanningState = {
      prompt: initialState.prompt || "",
      isInteractive: initialState.isInteractive || false,
      userAnswers: initialState.userAnswers || {},
      currentStep: initialState.currentStep || "analyze",
      // Always start with a fresh results object
      results: {
        wireframes: undefined,
        prd: undefined,
        summary: undefined,
        techstack: undefined,
        analyze: undefined,
        clarify: undefined,
        filesystem: undefined,
        workflow: undefined,
        tasks: undefined,
        "context-profile": undefined,
      },
      completed: false,
    }

    try {
      // Execute steps sequentially with dynamic step sequence
      let currentState = state
      let steps = ["analyze"] // Always start with analyze

      // Execute analyze step first to determine project type
      currentState = await this.executeStep("analyze", currentState)
      if (currentState.needsInput) {
        return currentState
      }

      // Get dynamic step sequence based on project type
      const projectType = currentState.results.analyze?.projectType
      steps = this.getStepsForProjectType(projectType)

      // Execute remaining steps (skip analyze since we already did it)
      for (const step of steps.slice(1)) {
        currentState = await this.executeStep(step, currentState)
        if (currentState.needsInput) {
          break // Stop execution if input is needed
        }
      }

      return currentState
    } catch (error) {
      console.error("Graph execution error:", error)
      throw error
    }
  }

  /**
   * Get step sequence based on project type
   */
  private getStepsForProjectType(projectType: string): string[] {
    const baseSteps = ["analyze", "clarify", "summary", "techstack", "prd"]

    // Add optional steps based on project type
    if (this.isAIAgentProject(projectType)) {
      baseSteps.push("context-profile")
    }

    // Add database step for projects that need databases
    if (this.needsDatabaseStep(projectType)) {
      baseSteps.push("database")
    }

    // Continue with common steps
    baseSteps.push("wireframes", "design", "filesystem", "workflow", "tasks")

    return baseSteps
  }

  /**
   * Check if project is an AI agent that needs context profiles
   */
  private isAIAgentProject(projectType: string): boolean {
    if (!projectType) return false
    const lowerType = projectType.toLowerCase()
    return lowerType.includes("agent") ||
           lowerType.includes("bot") ||
           lowerType.includes("ai") ||
           lowerType === "ai agent"
  }

  /**
   * Check if project needs database design step
   */
  private needsDatabaseStep(projectType: string): boolean {
    if (!projectType) return false
    const lowerType = projectType.toLowerCase()
    return lowerType.includes("web") ||
           lowerType.includes("app") ||
           lowerType.includes("api") ||
           lowerType.includes("system")
  }

  async executeStep(step: string, context: PlanningState, answer?: string): Promise<any> {
    // Update context with new answer if provided
    if (answer && context.question) {
      context.userAnswers[context.question.id] = answer
    }

    // Execute the specific step
    switch (step) {
      case "analyze":
        return await this.analyzePrompt(context)
      case "clarify":
        return await this.clarifyRequirements(context)
      case "summary":
        return await this.generateSummary(context)
      case "techstack":
        return await this.selectTechStack(context)
      case "prd":
        return await this.createPRD(context)
      case "context-profile":
        return await this.generateContextProfile(context)
      case "wireframes":
        return await this.designWireframes(context)
      case "design":
        return await this.createDesignGuidelines(context)
      case "database":
        return await this.designDatabaseSchema(context)
      case "filesystem":
        return await this.planFilesystem(context)
      case "workflow":
        return await this.defineWorkflow(context)
      case "tasks":
        return await this.breakdownTasks(context)
      default:
        throw new Error(`Unknown step: ${step}`)
    }
  }

  private async analyzePrompt(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `You are a senior project analyst. Analyze the given project prompt and extract key information.

    Analyze this project prompt and provide a structured analysis including:
    1. Project type (Web Application, Mobile App, AI Agent, etc.)
    2. Key features identified
    3. Complexity assessment (Simple, Medium, Complex)
    4. Domain/industry
    5. Technical requirements hints

    Respond in JSON format with these exact keys: projectType, features, complexity, domain, technicalHints`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project prompt: "${state.prompt}"`),
    ])

    try {
      const analysis = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          analyze: analysis,
        },
      }
    } catch (error) {
      // Fallback if JSON parsing fails
      return {
        ...state,
        results: {
          ...state.results,
          analyze: {
            projectType: "Web Application",
            features: ["Core Functionality"],
            complexity: "Medium",
            domain: "General",
            technicalHints: "Modern web technologies recommended",
          },
        },
      }
    }
  }

  private async clarifyRequirements(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive) {
      // Check if we need to ask questions
      const requiredQuestions = ["target_users", "platform"]
      const missingAnswers = requiredQuestions.filter((q) => !state.userAnswers[q])

      if (missingAnswers.length > 0) {
        const questionMap: Record<string, any> = {
          target_users: {
            id: "target_users",
            question: "Who are the primary users of this application?",
            type: "text",
            placeholder: "e.g., Small business owners, students, developers...",
          },
          platform: {
            id: "platform",
            question: "What platform should this run on?",
            type: "text",
            placeholder: "e.g., Web, mobile, desktop, or all platforms...",
          },
        }

        return {
          ...state,
          needsInput: true,
          question: questionMap[missingAnswers[0]],
        }
      }
    }

    // Generate clarification results
    const systemPrompt = `Based on the project analysis and user answers, provide detailed clarification of requirements.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    User Answers: ${JSON.stringify(state.userAnswers)}

    Provide clarification in JSON format with keys: targetUsers, platform, requirements, scope`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate detailed requirements clarification."),
    ])

    try {
      const clarification = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          clarify: clarification,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          clarify: {
            targetUsers: state.userAnswers.target_users || "General users",
            platform: state.userAnswers.platform || "Web application",
            requirements: "Requirements clarified based on user input",
            scope: "Project scope defined",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async generateSummary(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Create a comprehensive project summary based on the analysis and clarification.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}

    Generate a project summary in JSON format with keys: overview, scope, goals, keyFeatures`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate a comprehensive project summary."),
    ])

    try {
      const summary = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          summary: summary,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          summary: {
            overview: `A ${state.results.analyze?.projectType || "application"} that ${state.prompt}`,
            scope: `Targeting ${state.results.clarify?.targetUsers || "users"} on ${state.results.clarify?.platform || "web platform"}`,
            goals: state.results.analyze?.features || ["Core functionality"],
            keyFeatures: state.results.analyze?.features || ["Primary features"],
          },
        },
      }
    }
  }

  private async selectTechStack(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive && !state.userAnswers.preferences) {
      return {
        ...state,
        needsInput: true,
        question: {
          id: "preferences",
          question: "Any technology preferences?",
          type: "text",
          placeholder: "e.g., React, Python, specific databases...",
          optional: true,
        },
      }
    }

    const systemPrompt = `Recommend a technology stack based on the project requirements.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}
    User Preferences: ${state.userAnswers.preferences || "None specified"}

    Recommend technologies in JSON format with keys: frontend, backend, database, hosting, authentication, preferences`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Recommend an appropriate technology stack."),
    ])

    try {
      const techStack = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          techstack: techStack,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          techstack: {
            frontend: "React",
            backend: "Node.js",
            database: "PostgreSQL",
            hosting: "Vercel",
            authentication: "NextAuth.js",
            preferences: state.userAnswers.preferences || "Modern, scalable stack",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async createPRD(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive && !state.userAnswers.timeline) {
      return {
        ...state,
        needsInput: true,
        question: {
          id: "timeline",
          question: "What's your target timeline?",
          type: "text",
          placeholder: "e.g., 2 weeks, 1 month, 3 months...",
          optional: true,
        },
      }
    }

    const systemPrompt = `Create a Product Requirements Document based on all gathered information.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}
    Summary: ${JSON.stringify(state.results.summary)}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Timeline: ${state.userAnswers.timeline || "Not specified"}

    Generate PRD in JSON format with keys: timeline, features, userStories, requirements, acceptanceCriteria`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Create a comprehensive Product Requirements Document."),
    ])

    try {
      const prd = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          prd: prd,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          prd: {
            timeline: state.userAnswers.timeline || "8-12 weeks",
            features: state.results.analyze?.features || ["Core functionality"],
            userStories: [`As a user, I want to ${state.prompt.toLowerCase()}`],
            requirements: "Comprehensive requirements documented",
            acceptanceCriteria: "Acceptance criteria defined",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async generateContextProfile(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Create a comprehensive Structured JSON Context Profile Template for an AI agent.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Summary: ${JSON.stringify(state.results.summary)}
    PRD: ${JSON.stringify(state.results.prd)}

    Generate a detailed context profile template that defines the AI agent's identity, capabilities, goals, and operational parameters.

    Return a JSON object with this exact structure:
    {
      "profile_id": "agent-[domain]-[role]-v1.0.0",
      "identity": {
        "name": "[Agent Name]",
        "role": "[Specific Role]",
        "organization": "[Organization/Domain]",
        "timezone": "UTC",
        "language": "en-US"
      },
      "goals": {
        "short_term": ["goal1", "goal2"],
        "long_term": ["goal1", "goal2"]
      },
      "preferences": {
        "communication_style": "[style]",
        "response_format": "[format]",
        "tone": "[tone]",
        "visuals": true/false,
        "default_output_type": "[type]"
      },
      "capabilities": {
        "tools_enabled": ["tool1", "tool2"],
        "environment": {
          "platform": "[platform]",
          "extensions": ["ext1", "ext2"]
        }
      },
      "memory": {
        "scope": "[scope]",
        "persistence": "[type]",
        "structure": "[structure]",
        "data_points": ["point1", "point2"]
      },
      "constraints": {
        "rate_limit": "[limit]",
        "budget": {
          "monthly": 0,
          "used": 0
        },
        "operational_constraints": ["constraint1", "constraint2"]
      },
      "behavioral_flags": {
        "debug_mode": false,
        "auto_summarize": true/false,
        "use_context_window": true/false
      },
      "metadata": {
        "created_at": "[ISO timestamp]",
        "last_updated": "[ISO timestamp]",
        "version": "1.0.0"
      }
    }`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate the context profile template for this AI agent."),
    ])

    try {
      const contextProfile = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          "context-profile": contextProfile,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      // Fallback context profile template
      const fallbackProfile = {
        profile_id: "agent-assistant-v1.0.0",
        identity: {
          name: "AI Assistant",
          role: "General Purpose Assistant",
          organization: "AI Services",
          timezone: "UTC",
          language: "en-US"
        },
        goals: {
          short_term: ["Assist users with tasks", "Provide accurate information"],
          long_term: ["Improve user productivity", "Learn from interactions"]
        },
        preferences: {
          communication_style: "helpful_and_clear",
          response_format: "structured_text",
          tone: "professional_friendly",
          visuals: false,
          default_output_type: "text_response"
        },
        capabilities: {
          tools_enabled: ["text_processing", "information_retrieval"],
          environment: {
            platform: "Cloud-based",
            extensions: ["web_search", "document_analysis"]
          }
        },
        memory: {
          scope: "conversation",
          persistence: "session",
          structure: "contextual",
          data_points: ["user_preferences", "conversation_history"]
        },
        constraints: {
          rate_limit: "1000 requests/hour",
          budget: {
            monthly: 100,
            used: 0
          },
          operational_constraints: ["respect_privacy", "provide_accurate_info"]
        },
        behavioral_flags: {
          debug_mode: false,
          auto_summarize: true,
          use_context_window: true
        },
        metadata: {
          created_at: new Date().toISOString(),
          last_updated: new Date().toISOString(),
          version: "1.0.0"
        }
      }

      return {
        ...state,
        results: {
          ...state.results,
          "context-profile": fallbackProfile,
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async designWireframes(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Design wireframes based on the project requirements.

    Project: ${state.prompt}
    Summary: ${JSON.stringify(state.results.summary)}
    PRD: ${JSON.stringify(state.results.prd)}

    Generate wireframe plan in JSON format with keys: pages, components, responsive, navigation`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Design wireframes for the application."),
    ])

    try {
      const wireframes = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          wireframes: wireframes,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          wireframes: {
            pages: ["Landing Page", "Main Dashboard", "User Profile"],
            components: ["Header", "Navigation", "Content Area", "Footer"],
            responsive: true,
            navigation: "Standard web navigation pattern",
          },
        },
      }
    }
  }

  private async createDesignGuidelines(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Create design guidelines based on the project requirements and wireframes.

    Project: ${state.prompt}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Wireframes: ${JSON.stringify(state.results.wireframes)}

    Generate design guidelines in JSON format with keys: theme, colorPalette, typography, layout, interactive, effects, animations`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Create comprehensive design guidelines for the application."),
    ])

    try {
      const design = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          design: design,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          design: {
            theme: "Modern and clean design",
            colorPalette: {
              primary: "#007bff",
              secondary: "#6c757d",
              success: "#28a745",
              danger: "#dc3545",
            },
            typography: "Clean, readable fonts with proper hierarchy",
            layout: "Responsive grid-based layout",
            interactive: "Smooth hover effects and transitions",
            effects: "Subtle shadows and gradients",
            animations: "Smooth page transitions",
          },
        },
      }
    }
  }

  private async planFilesystem(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Plan the file system structure based on the technology stack.

    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Project Type: ${state.results.analyze?.projectType}

    Generate filesystem plan in JSON format with keys: structure, folders, files, organization`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Plan the project file system structure."),
    ])

    try {
      const filesystem = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          filesystem: filesystem,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          filesystem: {
            structure: "Modern project structure",
            folders: ["src/", "components/", "pages/", "utils/", "styles/"],
            files: "Key files identified and organized",
            organization: "Follows best practices for the selected tech stack",
          },
        },
      }
    }
  }

  private async defineWorkflow(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Define the workflow logic for the application.

    Project: ${state.prompt}
    Project Type: ${state.results.analyze?.projectType}
    Features: ${JSON.stringify(state.results.summary?.keyFeatures)}

    Generate workflow definition in JSON format with keys: steps, logic, integrations, dataFlow`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Define the application workflow logic."),
    ])

    try {
      const workflow = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          workflow: workflow,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          workflow: {
            steps: ["User Input", "Processing", "Data Storage", "Response"],
            logic: "Workflow logic defined based on project requirements",
            integrations: "API endpoints and external services planned",
            dataFlow: "Data flow patterns established",
          },
        },
      }
    }
  }

  private async breakdownTasks(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Break down the project into implementation tasks.

    Project: ${state.prompt}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    PRD: ${JSON.stringify(state.results.prd)}
    Timeline: ${state.results.prd?.timeline}

    Generate task breakdown in JSON format with keys: totalTasks, categories, estimate, priority, phases`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Break down the project into actionable implementation tasks."),
    ])

    try {
      const tasks = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          tasks: tasks,
        },
        completed: true,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          tasks: {
            totalTasks: 24,
            categories: ["Setup", "Frontend", "Backend", "Testing", "Deployment"],
            estimate: state.results.prd?.timeline || "8-12 weeks",
            priority: "High priority tasks identified",
            phases: "Development phases planned",
          },
        },
        completed: true,
      }
    }
  }

  private async designDatabaseSchema(state: PlanningState): Promise<PlanningState> {
    const systemPrompt = `Design a database schema for the project.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    PRD: ${JSON.stringify(state.results.prd)}
    Tech Stack: ${JSON.stringify(state.results.techstack)}

    Generate database schema in JSON format with keys: tables, relationships, indexes, constraints`

    const response = await this.model.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Design the database schema for this application."),
    ])

    try {
      const database = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          database: database,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          database: {
            tables: ["users", "sessions", "data"],
            relationships: "Standard relational structure",
            indexes: "Performance optimized indexes",
            constraints: "Data integrity constraints",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }
}
